'use client';

'use client';

import React from 'react';
import Link from 'next/link';
import IconRenderer from './IconRenderer';

const SidebarItem = ({ icon, label, to, isSelected, isLogout = false, onClick }) => (
  <Link href={to} onClick={onClick} className="block">
    <div className="flex items-center gap-3 px-4 py-2 font-medium text-sm cursor-pointer hover:bg-gray-50 rounded-lg transition-colors">
      <IconRenderer icon={icon} isSelected={isSelected} isLogout={isLogout} />
      <span className={`font-semibold ${isSelected ? 'text-gray-600' : 'text-gray-400'}`}>{label}</span>
    </div>
  </Link>
);

export default SidebarItem;
